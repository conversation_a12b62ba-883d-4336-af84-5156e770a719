import * as vscode from 'vscode';
import * as path from 'path';
import * as fs from 'fs';
import { generateWebviewHtml } from '../templates/webviewTemplate';
import { WebviewContext } from '../types/context';
import { MastraAIService } from '../services/aiService';

export class ReactWebviewProvider implements vscode.WebviewViewProvider {
    private _view?: vscode.WebviewView;
    private _aiService: MastraAIService;

    constructor(
        private readonly _extensionUri: vscode.Uri,
        private readonly _viewId: string
    ) {
        this._aiService = new MastraAIService();
    }

    public resolveWebviewView(
        webviewView: vscode.WebviewView,
        _context: vscode.WebviewViewResolveContext,
        _token: vscode.CancellationToken,
    ) {
        console.log(`Resolving webview view for ${this._viewId}...`);
        this._view = webviewView;

        webviewView.webview.options = {
            enableScripts: true,
            localResourceRoots: [
                this._extensionUri
            ]
        };
        const webviewContext: WebviewContext = {
            layout: 'sidebar',
            extensionUri: this._extensionUri.toString()
        };
        try {
            const html = this.getHtmlForWebview(webviewView.webview);
            console.log('Generated HTML length:', html.length);
            console.log('HTML preview:', html);
            webviewView.webview.html = generateWebviewHtml(
                webviewView.webview,
            this._extensionUri,
            webviewContext
            );
            console.log('Webview HTML set successfully');
        } catch (error) {
            console.error('Error setting webview HTML:', error);
            // 提供一个简单的fallback
            webviewView.webview.html = `
                <!DOCTYPE html>
                <html>
                <head>
                    <meta charset="UTF-8">
                    <title>AI Code</title>
                </head>
                <body>
                    <h1>AI Code Extension</h1>
                    <p>Webview加载成功！</p>
                    <button onclick="testMessage()">测试消息</button>
                    <script>
                        const vscode = acquireVsCodeApi();
                        function testMessage() {
                            vscode.postMessage({ command: 'alert', data: 'Hello from ${this._viewId}!' });
                        }
                    </script>
                </body>
                </html>
            `;
        }

        // 监听消息
        webviewView.webview.onDidReceiveMessage(async message => {
            console.log('事件监听', message);

            switch (message.command) {
                case 'showMessage':
                    vscode.window.showInformationMessage(message.text);
                    break;

                case 'sendChatMessage':
                    await this.handleChatMessage(message.message, message.messages);
                    break;

                case 'alert':
                    vscode.window.showInformationMessage(message.data);
                    break;
            }
        });
    }

    private getHtmlForWebview(webview: vscode.Webview) {
        // 获取webview脚本的URI
        const scriptUri = webview.asWebviewUri(vscode.Uri.joinPath(this._extensionUri, 'dist', 'webview.js'));
        console.log('Script URI:', scriptUri.toString());

        // 检查文件是否存在
        const scriptPath = path.join(this._extensionUri.fsPath, 'dist', 'webview.js');
        const scriptExists = fs.existsSync(scriptPath);
        console.log('Script file exists:', scriptExists, 'at path:', scriptPath);

        // 读取HTML模板
        const htmlPath = path.join(this._extensionUri.fsPath, 'src', 'webview', 'index.html');
        let html;

        try {
            html = fs.readFileSync(htmlPath, 'utf8');
            console.log('HTML template loaded successfully');
        } catch (error) {
            console.error('Failed to read HTML template:', error);
            // 提供一个简单的fallback HTML
            html = `<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Code Extension</title>
</head>
<body>
    <div id="root">
        <h1>AI Code Extension</h1>
        <p>React应用加载中...</p>
        <button onclick="testMessage()">测试消息</button>
    </div>
    <script>
        const vscode = acquireVsCodeApi();
        function testMessage() {
            vscode.postMessage({ command: 'alert', data: 'Hello from webview!' });
        }
    </script>
    <script src="{{webviewScript}}"></script>
</body>
</html>`;
        }

        // 替换脚本路径
        html = html.replace('{{webviewScript}}', scriptUri.toString());

        return html;
    }

    public sendMessage(command: string, data?: any) {
        if (this._view) {
            this._view.webview.postMessage({ command, data });
        }
    }

    private async handleChatMessage(userMessage: string, previousMessages: any[] = []) {
        if (!this._view) {
            return;
        }

        try {
            // 检查 API Key
            // if (!this._aiService.hasApiKey()) {
            //     this._view.webview.postMessage({
            //         command: 'aiError',
            //         error: 'OpenAI API Key 未配置。请在 VS Code 设置中配置 aicode.openaiApiKey。'
            //     });
            //     return;
            // }

            // 构建消息历史
            const messages = [
                ...previousMessages,
                { role: 'user', content: userMessage }
            ];

            // 使用流式响应
            await this._aiService.query(
                userMessage,
                messages,
                {},
                undefined,
                (chunk) => {
                    console.log('接收到流数据：', chunk);
                    // 发送流式数据到 webview
                    this._view?.webview.postMessage({
                        command: 'aiStreamChunk',
                        content: chunk.content
                    });
                }
            );

            // 发送流结束信号
            this._view.webview.postMessage({
                command: 'aiStreamEnd'
            });

        } catch (error: any) {
            console.error('AI chat error:', error);
            this._view.webview.postMessage({
                command: 'aiError',
                error: error.message || '发生未知错误'
            });
        }
    }

}